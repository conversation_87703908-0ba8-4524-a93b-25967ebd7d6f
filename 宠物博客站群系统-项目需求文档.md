# 宠物博客站群系统项目需求文档

## 一、项目概述

### 1.1 项目背景
构建一个面向国际市场的宠物知识分享博客站群系统，专注于猫狗相关内容，通过多语言独立站点覆盖不同国家市场，实现内容的规模化运营。

### 1.2 目标用户
- **内容消费者**：美国、德国、俄罗斯等国家的宠物爱好者
- **内容管理者**：通过统一后台管理多语言站点的运营人员

### 1.3 核心价值主张
- 提供专业、本地化的宠物知识内容
- 通过SEO优化获取自然流量
- 一次创作，多语言分发，提高内容生产效率

## 二、技术架构

### 2.1 技术栈选型
- **前端框架**：Astro（SSG/SSR混合，利于SEO）
- **后端框架**：Node.js + Express.js
- **数据库**：MySQL 9.0.1（远程）
- **缓存层**：Redis
- **部署环境**：Linux + 宝塔面板
- **AI翻译**：OpenAI接口规范（Gemini-2.5-pro）

### 2.2 架构设计原则
- **独立模板策略**：每种语言使用独立前端模板，不使用i18n
- **数据隔离**：各语言站点的文章、分类、评论数据完全独立
- **域名路由**：通过域名识别展示对应语言模板
- **缓存优先**：使用Redis缓存热点数据，提升性能

## 三、功能需求详细说明

### 3.1 前端页面需求

#### 3.1.1 页面清单
1. **首页** (/)
   - 最新文章列表（分页，每页10篇）
   - 热门文章展示（最近7天浏览量Top 5）
   - 分类导航
   - 搜索框

2. **分类页** (/category/[slug])
   - 面包屑导航
   - 该分类下的文章列表（分页，每页12篇）
   - 子分类展示（如有）

3. **文章详情页** (/post/[slug])
   - 面包屑导航
   - 文章标题、发布时间、浏览量
   - 文章内容（富文本）
   - 社交分享按钮
   - 相关文章推荐（同分类随机5篇，不重复）
   - 评论区（多层嵌套）

4. **搜索结果页** (/search)
   - 搜索框
   - 搜索结果列表（分页，每页10篇）
   - 搜索范围：标题+内容

5. **404错误页** (/404)
   - 友好的错误提示
   - 返回首页链接
   - 推荐热门文章

6. **关于页面** (/about)
   - 网站介绍
   - 联系方式

#### 3.1.2 分类结构
```
- 猫咪
  ├── 日常护理
  ├── 健康医疗
  ├── 行为训练
  └── 饮食营养
- 狗狗
  ├── 日常护理
  ├── 健康医疗
  ├── 行为训练
  └── 饮食营养
```

#### 3.1.3 前后端交互接口
- GET /api/posts - 获取文章列表
- GET /api/posts/[id] - 获取文章详情
- GET /api/categories - 获取分类列表
- GET /api/posts/hot - 获取热门文章
- GET /api/posts/related - 获取相关文章
- POST /api/comments - 提交评论
- GET /api/comments/[postId] - 获取文章评论
- POST /api/search - 搜索文章
- GET /api/stats/view - 记录浏览量

### 3.2 后台管理系统需求

#### 3.2.1 域名管理
- 域名绑定语言配置
- 域名状态监控
- SSL证书管理提醒

#### 3.2.2 内容管理
**文章管理**
- 富文本编辑器（支持图片粘贴上传）
- 文章状态：草稿、待发布、已发布
- SEO设置：标题、描述、关键词
- 图片ALT文本设置
- 版本历史管理
- 批量操作：批量删除、批量发布

**翻译工作流**
1. 创建中文原始文章
2. 点击"翻译"按钮，调用AI接口
3. 生成多语言草稿
4. 人工校对修改
5. 发布到对应语言站点

**分类管理**
- 多语言分类名称设置
- 分类排序
- 分类文章数统计

#### 3.2.3 评论管理
- 评论审核队列
- 批量审核
- 评论屏蔽词设置
- 垃圾评论过滤

#### 3.2.4 媒体管理
- 图片上传（本地存储）
- 自动压缩优化
- 生成多尺寸缩略图
- WebP格式转换
- 图片库管理

#### 3.2.5 广告与统计
- 每个语言站点独立配置
- Google AdSense代码设置
- Google Analytics代码设置
- 广告开关控制
- 广告位置：
  - 文章内容中间（第3段后）
  - 文章底部（评论上方）
  - 侧边栏顶部

#### 3.2.6 系统设置
- AI接口配置（API地址、密钥、模型）
- 缓存策略设置
- SEO全局设置
- 站点基本信息

### 3.3 SEO优化需求

#### 3.3.1 URL结构
- 本地化URL Slug
  - 英语：/category/cat-care/how-to-feed-kittens
  - 德语：/kategorie/katzenpflege/wie-fuettert-man-kaetzchen
  - 俄语：/категория/уход-за-кошками/как-кормить-котят

#### 3.3.2 技术SEO
- XML Sitemap自动生成
- RSS Feed订阅
- robots.txt配置
- 结构化数据（Schema.org）
  - Article
  - BreadcrumbList
  - WebSite
- Open Graph标签
- Twitter Card标签
- 规范化标签（Canonical）

#### 3.3.3 性能优化
- 页面加载速度<3秒
- 图片懒加载
- CSS/JS压缩
- CDN支持（预留）
- Redis缓存策略
  - 热门文章缓存24小时
  - 分类列表缓存6小时
  - 文章内容缓存1小时

## 四、非功能性需求

### 4.1 性能要求
- 前期并发访问：支持1万日访问量
- 页面响应时间：<2秒
- API响应时间：<500ms
- 数据库查询优化：使用索引

### 4.2 安全要求
- SQL注入防护
- XSS攻击防护
- CSRF防护
- 敏感信息加密存储
- API密钥安全管理

### 4.3 可扩展性
- 模块化代码结构
- 新语言模板快速复制部署
- 数据库分表设计（按语言）
- 微服务架构预留

### 4.4 可维护性
- 完整的代码注释
- API文档
- 部署文档
- 新语言添加指南

## 五、数据库设计

### 5.1 核心表结构

#### 5.1.1 sites（站点配置表）
```sql
- id: INT PRIMARY KEY
- domain: VARCHAR(255) UNIQUE
- language: VARCHAR(10)
- language_name: VARCHAR(50)
- site_name: VARCHAR(255)
- site_description: TEXT
- is_active: BOOLEAN
- ga_code: TEXT
- adsense_code: TEXT
- ads_enabled: BOOLEAN
- created_at: DATETIME
- updated_at: DATETIME
```

#### 5.1.2 posts_{lang}（文章表，按语言分表）
```sql
- id: INT PRIMARY KEY
- title: VARCHAR(255)
- slug: VARCHAR(255) UNIQUE
- content: LONGTEXT
- excerpt: TEXT
- category_id: INT
- subcategory_id: INT
- meta_title: VARCHAR(255)
- meta_description: TEXT
- meta_keywords: TEXT
- featured_image: VARCHAR(500)
- status: ENUM('draft', 'pending', 'published')
- view_count: INT DEFAULT 0
- original_post_id: INT (关联中文原始文章)
- version: INT
- published_at: DATETIME
- created_at: DATETIME
- updated_at: DATETIME
```

#### 5.1.3 categories_{lang}（分类表）
```sql
- id: INT PRIMARY KEY
- name: VARCHAR(100)
- slug: VARCHAR(100) UNIQUE
- parent_id: INT DEFAULT NULL
- description: TEXT
- sort_order: INT
- post_count: INT DEFAULT 0
- created_at: DATETIME
- updated_at: DATETIME
```

#### 5.1.4 comments_{lang}（评论表）
```sql
- id: INT PRIMARY KEY
- post_id: INT
- parent_id: INT DEFAULT NULL
- author_name: VARCHAR(100)
- author_email: VARCHAR(255)
- content: TEXT
- status: ENUM('pending', 'approved', 'rejected')
- ip_address: VARCHAR(45)
- created_at: DATETIME
- updated_at: DATETIME
```

#### 5.1.5 media（媒体文件表）
```sql
- id: INT PRIMARY KEY
- filename: VARCHAR(500)
- original_name: VARCHAR(500)
- file_path: VARCHAR(1000)
- file_size: BIGINT
- mime_type: VARCHAR(100)
- width: INT
- height: INT
- alt_text_en: VARCHAR(500)
- alt_text_de: VARCHAR(500)
- alt_text_ru: VARCHAR(500)
- thumbnails: JSON
- created_at: DATETIME
```

#### 5.1.6 translations（翻译记录表）
```sql
- id: INT PRIMARY KEY
- original_post_id: INT
- target_language: VARCHAR(10)
- translated_post_id: INT
- translation_status: ENUM('pending', 'completed', 'failed')
- ai_model: VARCHAR(50)
- created_at: DATETIME
- updated_at: DATETIME
```

#### 5.1.7 admin_users（管理员表）
```sql
- id: INT PRIMARY KEY
- username: VARCHAR(50) UNIQUE
- password: VARCHAR(255)
- email: VARCHAR(255)
- last_login: DATETIME
- created_at: DATETIME
- updated_at: DATETIME
```

#### 5.1.8 settings（系统设置表）
```sql
- id: INT PRIMARY KEY
- setting_key: VARCHAR(100) UNIQUE
- setting_value: TEXT
- setting_type: VARCHAR(50)
- description: TEXT
- updated_at: DATETIME
```

#### 5.1.9 post_versions（文章版本历史表）
```sql
- id: INT PRIMARY KEY
- post_id: INT
- language: VARCHAR(10)
- version_number: INT
- title: VARCHAR(255)
- content: LONGTEXT
- created_by: INT
- created_at: DATETIME
```

#### 5.1.10 post_stats（文章统计表）
```sql
- id: INT PRIMARY KEY
- post_id: INT
- language: VARCHAR(10)
- date: DATE
- view_count: INT DEFAULT 0
- unique_visitors: INT DEFAULT 0
- created_at: DATETIME
- updated_at: DATETIME
```

### 5.2 数据隔离策略
每个语言站点使用独立的数据表，通过表名后缀区分（如posts_en, posts_de, posts_ru）

### 5.3 索引策略
- posts表：slug, status, category_id, published_at
- categories表：slug, parent_id
- comments表：post_id, status
- post_stats表：post_id, date

## 六、开发环境配置

### 6.1 本地开发
- 使用hosts文件模拟多域名
- 环境变量配置不同语言模板
- 远程数据库连接
- 本地Redis服务

### 6.2 测试域名方案
```
127.0.0.1 test-en.local
127.0.0.1 test-de.local
127.0.0.1 test-ru.local
```

### 6.3 开发环境变量配置
```env
# 数据库配置
DB_HOST=************
DB_NAME=bengtai
DB_USER=bengtai
DB_PASSWORD=weizhen258

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# AI翻译配置
AI_API_URL=https://ai.wanderintree.top
AI_API_KEY=sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
AI_MODEL=gemini-2.5-pro

# 本地开发
NODE_ENV=development
PORT=3000
ADMIN_PORT=3001
```

## 七、项目里程碑

### Phase 1：基础架构（2周）
- 项目初始化
- 数据库设计与创建
- 后端API框架搭建
- 管理后台基础界面

### Phase 2：核心功能（3周）
- 文章管理系统
- AI翻译集成
- 评论系统
- 媒体处理模块

### Phase 3：前端开发（3周）
- 英语模板开发
- 德语模板开发
- 俄语模板开发
- SEO优化实施

### Phase 4：优化部署（1周）
- 性能优化
- 缓存策略实施
- 部署配置
- 测试验收

## 八、风险评估与应对

### 8.1 技术风险
- **风险**：多域名识别技术复杂度
- **应对**：使用Nginx反向代理+Express中间件双重识别

### 8.2 性能风险
- **风险**：翻译API调用延迟
- **应对**：异步队列处理，避免阻塞

### 8.3 运营风险
- **风险**：SEO效果不理想
- **应对**：持续优化，A/B测试，数据驱动决策

### 8.4 安全风险
- **风险**：数据库凭证泄露
- **应对**：使用环境变量，不在代码中硬编码

## 九、成功指标（KPI）

### 9.1 技术指标
- 页面加载速度 < 3秒
- 系统可用性 > 99%
- API响应时间 < 500ms
- 并发用户支持 > 100

### 9.2 SEO指标
- 3个月内Google收录 > 80%页面
- 6个月内关键词排名进入前3页
- 页面跳出率 < 60%
- 平均停留时间 > 2分钟

### 9.3 业务指标
- 6个月内日均访问量达到1万
- 内容发布效率提升300%
- 多语言覆盖率100%

### 9.4 用户指标
- 评论参与率 > 5%
- 文章分享率 > 10%
- 回访率 > 30%

## 十、后续扩展计划

### 10.1 短期计划（3-6个月）
1. 支持更多语言（法语、西班牙语）
2. 增加视频内容支持
3. 开发移动端响应式优化
4. 增加社交媒体自动分享

### 10.2 中期计划（6-12个月）
1. 用户社区功能
2. 宠物问答板块
3. 专家认证体系
4. 内容付费功能

### 10.3 长期计划（12个月以上）
1. 移动APP开发
2. 电商功能集成（宠物用品推荐）
3. AI宠物健康顾问
4. 线下活动组织平台

## 十一、项目交付标准

### 11.1 代码质量
- 代码注释覆盖率 > 30%
- 单元测试覆盖率 > 60%
- 无严重安全漏洞
- 遵循ESLint规范

### 11.2 文档要求
- 完整的API文档
- 部署操作手册
- 管理后台使用手册
- 新语言添加指南

### 11.3 性能基准
- 首页加载时间 < 2秒
- 文章页加载时间 < 3秒
- 搜索响应时间 < 1秒
- 图片优化率 > 80%

## 十二、项目维护计划

### 12.1 日常维护
- 每日数据库备份（由宝塔面板处理）
- 每周性能监控报告
- 每月安全更新检查
- 实时错误日志监控

### 12.2 内容运营
- 每日发布5篇文章
- 每日评论审核
- 每周数据分析
- 每月SEO优化调整

### 12.3 技术支持
- Bug修复响应时间 < 24小时
- 功能更新周期：每月1次
- 紧急问题响应时间 < 2小时

---

**文档版本**：v1.0  
**创建日期**：2025-08-11  
**最后更新**：2025-08-11  
**文档状态**：已批准  
**项目负责人**：待定  
**技术负责人**：待定  

## 附录A：技术参考资料

### A.1 Astro官方文档
- https://astro.build/
- SSG/SSR最佳实践
- SEO优化指南

### A.2 Google SEO指南
- https://developers.google.com/search/docs
- 结构化数据实施
- Core Web Vitals优化

### A.3 Redis缓存策略
- 缓存穿透防护
- 缓存雪崩处理
- 热点数据识别

## 附录B：新语言模板添加流程

### B.1 准备工作
1. 复制现有语言模板目录
2. 准备翻译好的UI文本
3. 准备本地化的URL规则

### B.2 实施步骤
1. 创建新语言数据表
2. 配置语言路由规则
3. 替换模板中的文本
4. 测试各页面功能
5. 配置SEO元数据
6. 部署到生产环境

### B.3 验收标准
- 所有页面正常显示
- URL符合本地化规范
- SEO标签正确配置
- 性能指标达标

## 附录C：应急预案

### C.1 服务器故障
- 启用备用服务器
- 恢复最近备份
- 通知用户维护

### C.2 数据库故障
- 切换到从库
- 恢复数据备份
- 数据一致性检查

### C.3 API服务异常
- 启用本地缓存
- 降级服务方案
- 手动内容发布